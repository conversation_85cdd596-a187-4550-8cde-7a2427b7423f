#!/usr/bin/env node

/**
 * Debug script to analyze transcription segments and speaker detection
 */

const { transcribeFile } = require('./transcribe-mp3.js');
const path = require('path');

async function debugTranscription() {
  const filename = process.argv[2] || "Monday at 11-06.mp3";
  const filePath = path.resolve(filename);
  
  try {
    console.log(`🔍 Debug analysis for: ${filename}`);
    console.log('='.repeat(60));
    
    // Get raw transcription data
    const transcription = await transcribeFile(filePath);
    
    console.log('\n📊 Raw transcription data:');
    console.log('Text length:', transcription.text?.length || 0);
    console.log('Segments count:', transcription.segments?.length || 0);
    console.log('Language:', transcription.language);
    console.log('Duration:', transcription.duration);
    
    if (transcription.segments && transcription.segments.length > 0) {
      console.log('\n📝 Segment details:');
      console.log('─'.repeat(80));
      
      transcription.segments.forEach((segment, index) => {
        const duration = segment.end - segment.start;
        console.log(`${index + 1}. [${segment.start.toFixed(3)}s - ${segment.end.toFixed(3)}s] (${duration.toFixed(3)}s)`);
        console.log(`   Text: "${segment.text.trim()}"`);
        console.log(`   avg_logprob: ${segment.avg_logprob?.toFixed(3) || 'N/A'}`);
        console.log(`   no_speech_prob: ${segment.no_speech_prob?.toFixed(3) || 'N/A'}`);
        
        if (index > 0) {
          const prevSegment = transcription.segments[index - 1];
          const gap = segment.start - prevSegment.end;
          console.log(`   Gap from previous: ${gap.toFixed(3)}s`);
        }
        console.log('');
      });
      
      // Analyze potential speaker changes
      console.log('\n🗣️ Speaker change analysis:');
      console.log('─'.repeat(50));
      
      for (let i = 1; i < transcription.segments.length; i++) {
        const current = transcription.segments[i];
        const previous = transcription.segments[i - 1];
        
        const gap = current.start - previous.end;
        const logprobDiff = Math.abs((current.avg_logprob || 0) - (previous.avg_logprob || 0));
        const noSpeechDiff = Math.abs((current.no_speech_prob || 0) - (previous.no_speech_prob || 0));
        
        let changeScore = 0;
        let reasons = [];
        
        if (gap > 1.0) {
          changeScore += 2;
          reasons.push(`silence gap: ${gap.toFixed(3)}s`);
        }
        
        if (logprobDiff > 0.3) {
          changeScore += 3;
          reasons.push(`logprob diff: ${logprobDiff.toFixed(3)}`);
        } else if (logprobDiff > 0.15) {
          changeScore += 1;
          reasons.push(`small logprob diff: ${logprobDiff.toFixed(3)}`);
        }
        
        if (noSpeechDiff > 0.05) {
          changeScore += 1;
          reasons.push(`speech prob diff: ${noSpeechDiff.toFixed(3)}`);
        }
        
        if (changeScore >= 2) {
          console.log(`🔄 Potential speaker change at segment ${i + 1}:`);
          console.log(`   Score: ${changeScore}, Reasons: ${reasons.join(', ')}`);
          console.log(`   Previous: "${previous.text.trim()}"`);
          console.log(`   Current: "${current.text.trim()}"`);
          console.log('');
        }
      }
    } else {
      console.log('\n⚠️ No segments found in transcription data');
      console.log('Raw text:', transcription.text);
    }
    
  } catch (error) {
    console.error('❌ Debug analysis failed:', error.message);
  }
}

debugTranscription();
