{"name": "mp3-transcriber", "version": "1.0.0", "description": "A Node.js script that processes MP3 files and generates transcripts using OpenAI's Whisper API", "main": "transcribe-mp3.js", "scripts": {"start": "node transcribe-mp3.js", "transcribe": "node transcribe-mp3.js", "test-setup": "node test-setup.js", "test-single": "node test-single-file.js", "test": "npm run test-setup"}, "keywords": ["transcription", "whisper", "openai", "mp3", "audio", "speech-to-text", "hungarian"], "author": "", "license": "MIT", "dependencies": {"openai": "^4.20.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": ""}, "bugs": {"url": ""}, "homepage": ""}