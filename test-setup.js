#!/usr/bin/env node

const { findMP3Files, secondsToSRTTime } = require('./transcribe-mp3.js');

async function testSetup() {
  console.log('🧪 Testing MP3 Transcriber Setup');
  console.log('='.repeat(40));
  
  try {
    // Test 1: Check if we can find MP3 files
    console.log('1. Testing MP3 file detection...');
    const mp3Files = await findMP3Files();
    console.log(`   ✅ Found ${mp3Files.length} MP3 files`);
    
    if (mp3Files.length > 0) {
      console.log('   📋 First few files:');
      mp3Files.slice(0, 3).forEach((file, index) => {
        console.log(`      ${index + 1}. ${file}`);
      });
      if (mp3Files.length > 3) {
        console.log(`      ... and ${mp3Files.length - 3} more`);
      }
    }
    
    // Test 2: Check timestamp formatting
    console.log('\n2. Testing timestamp formatting...');
    const testTimes = [0, 1.5, 65.123, 3661.456];
    testTimes.forEach(time => {
      const formatted = secondsToSRTTime(time);
      console.log(`   ${time}s → ${formatted}`);
    });
    console.log('   ✅ Timestamp formatting works correctly');
    
    // Test 3: Check OpenAI package
    console.log('\n3. Testing OpenAI package import...');
    const OpenAI = require('openai');
    console.log('   ✅ OpenAI package imported successfully');
    
    console.log('\n🎉 Setup test completed successfully!');
    console.log('\n📝 To start transcription, run:');
    console.log('   npm start');
    console.log('   or');
    console.log('   node transcribe-mp3.js');
    
  } catch (error) {
    console.error('❌ Setup test failed:', error.message);
    process.exit(1);
  }
}

testSetup();
