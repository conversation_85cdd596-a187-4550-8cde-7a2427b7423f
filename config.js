/**
 * Configuration file for MP3 Transcriber
 * Modify these settings as needed
 */

module.exports = {
  // OpenAI API Configuration
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',
    model: 'whisper-1',
    language: 'hu', // Hungarian
    responseFormat: 'verbose_json'
  },

  // Processing Configuration
  processing: {
    // Delay between API requests (milliseconds)
    delayBetweenRequests: 2000,
    
    // Silence gap threshold for speaker detection (seconds)
    speakerChangeThreshold: 2.0,
    
    // Skip files that already have transcripts
    skipExisting: true,
    
    // Maximum retries for failed requests
    maxRetries: 3,
    
    // Retry delay for rate limits (milliseconds)
    rateLimitRetryDelay: 60000
  },

  // Output Configuration
  output: {
    // File extension for transcript files
    transcriptExtension: '.txt',
    
    // Include timestamps in output
    includeTimestamps: true,
    
    // Include speaker labels
    includeSpeakerLabels: true,
    
    // Number of speakers to detect (for simple alternating detection)
    maxSpeakers: 2
  },

  // Logging Configuration
  logging: {
    // Show detailed progress information
    verbose: true,
    
    // Show file processing details
    showFileDetails: true,
    
    // Show API response details (for debugging)
    showApiDetails: false
  }
};
