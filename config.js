/**
 * Configuration file for MP3 Transcriber
 * Modify these settings as needed
 */

module.exports = {
  // OpenAI API Configuration
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',
    model: 'gpt-4o-transcribe', // Higher quality transcription model (fallback to whisper-1 if not available)
    fallbackModel: 'whisper-1', // Fallback model if primary model fails
    language: 'hu', // Hungarian
    responseFormat: 'verbose_json'
  },

  // Processing Configuration
  processing: {
    // Delay between API requests (milliseconds)
    delayBetweenRequests: 2000,

    // Skip files that already have transcripts
    skipExisting: true,

    // Maximum retries for failed requests
    maxRetries: 3,

    // Retry delay for rate limits (milliseconds)
    rateLimitRetryDelay: 60000
  },

  // Output Configuration
  output: {
    // File extension for transcript files
    transcriptExtension: '.txt',

    // Include timestamps in output
    includeTimestamps: true,

    // Include speaker labels
    includeSpeakerLabels: true,

    // Speaker detection configuration
    speakerDetection: {
      // Use advanced speaker detection based on audio characteristics
      useAdvancedDetection: true,

      // Minimum silence gap to consider speaker change (seconds)
      minSilenceGap: 1.0,

      // Minimum segment length to consider for speaker change (seconds)
      minSegmentLength: 0.5,

      // Voice activity threshold for speaker detection
      voiceActivityThreshold: 0.1
    }
  },

  // Logging Configuration
  logging: {
    // Show detailed progress information
    verbose: true,
    
    // Show file processing details
    showFileDetails: true,
    
    // Show API response details (for debugging)
    showApiDetails: false
  }
};
