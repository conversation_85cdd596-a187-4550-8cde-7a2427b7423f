#!/usr/bin/env node

/**
 * Test script to transcribe a single MP3 file
 * Usage: node test-single-file.js "filename.mp3"
 */

const { transcribeFile, saveTranscription } = require('./transcribe-mp3.js');
const path = require('path');
const fs = require('fs').promises;

async function testSingleFile() {
  const filename = process.argv[2];
  
  if (!filename) {
    console.log('❌ Please provide a filename as an argument');
    console.log('Usage: node test-single-file.js "Monday at 11-06.mp3"');
    process.exit(1);
  }
  
  const filePath = path.resolve(filename);
  
  try {
    // Check if file exists
    await fs.access(filePath);
    console.log(`🎵 Testing transcription for: ${filename}`);
    console.log('─'.repeat(50));
    
    // Transcribe the file
    console.log('🔄 Starting transcription...');
    const transcription = await transcribeFile(filePath);
    
    // Save the transcript
    console.log('💾 Saving transcript...');
    await saveTranscription(filePath, transcription);
    
    console.log('✅ Test completed successfully!');
    
    // Show a preview of the transcription
    if (transcription.text) {
      console.log('\n📝 Preview of transcription:');
      console.log('─'.repeat(30));
      const preview = transcription.text.substring(0, 200);
      console.log(preview + (transcription.text.length > 200 ? '...' : ''));
    }
    
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.error(`❌ File not found: ${filename}`);
      console.log('💡 Make sure the file exists in the current directory');
    } else {
      console.error('❌ Error during transcription:', error.message);
    }
    process.exit(1);
  }
}

testSingleFile();
