# MP3 Transcriber with OpenAI Whisper

A Node.js script that automatically processes all MP3 files in the current directory and generates Hungarian transcripts using OpenAI's Whisper API with speaker diarization and SRT-formatted timestamps.

## Features

- 🎵 **Batch Processing**: Automatically finds and processes all MP3 files in the current directory
- 🗣️ **Advanced Speaker Diarization**: Intelligent speaker detection that distinguishes between different speakers
- 👥 **Smart Speaker Grouping**: Connects text from the same speaker until speaker changes
- ⏰ **SRT Timestamps**: Generates timestamps in SRT format (HH:MM:SS,mmm)
- 🇭🇺 **Hungarian Language**: Optimized for Hungarian language transcription
- 🤖 **Best AI Model**: Uses `gpt-4o-transcribe` for superior transcription quality (fallback to `whisper-1`)
- 📝 **Verbose Output**: Detailed transcription with segment-level timestamps
- 🔄 **Error Handling**: Robust error handling with retry logic for rate limits
- 📊 **Progress Tracking**: Clear console output showing processing progress
- ⏭️ **Skip Existing**: Automatically skips files that already have transcripts
- ⚙️ **Configurable**: Easy-to-adjust settings for speaker detection sensitivity

## Prerequisites

- Node.js 18.0.0 or higher
- OpenAI API key with access to Whisper API
- MP3 files in the current directory

## Installation

1. **Clone or download the script files** to your directory containing MP3 files

2. **Install dependencies**:
   ```bash
   npm install
   ```

   Or if you prefer yarn:
   ```bash
   yarn install
   ```

## Usage

1. **Place the script files** in the same directory as your MP3 files

2. **Run the transcription script**:
   ```bash
   npm start
   ```
   
   Or directly:
   ```bash
   node transcribe-mp3.js
   ```

3. **Monitor progress** in the console. The script will:
   - Scan for all MP3 files
   - Process each file individually
   - Generate `.txt` files with the same base name as each MP3
   - Show progress and any errors encountered

## Output Format

The generated transcript files will contain:

```
00:00:00,119 --> 00:00:02,659 [Speaker 0] 
Tré, az egy nagyon menő könyv volt.  
 
00:00:02,659 --> 00:00:04,440 [Speaker 1] 
De nem tiltott könyv.  

00:00:04,440 --> 00:00:07,200 [Speaker 0]
Igen, de azért érdekes volt olvasni.
```

## Configuration

The script uses the following default settings:

- **Model**: `gpt-4o-transcribe` (with `whisper-1` fallback)
- **Language**: Hungarian (`hu`)
- **Response Format**: `verbose_json` with segment timestamps
- **Speaker Detection**: Advanced algorithm using audio characteristics and timing patterns
- **Speaker Grouping**: Intelligent grouping that connects consecutive segments from the same speaker

### API Key

The OpenAI API key is currently hardcoded in the script. For production use, consider:

1. Using environment variables:
   ```bash
   export OPENAI_API_KEY="your-api-key-here"
   node transcribe-mp3.js
   ```

2. Creating a `.env` file (requires `dotenv` package)

## Error Handling

The script includes comprehensive error handling:

- **Rate Limits**: Automatically waits and retries when hitting API rate limits
- **File I/O Errors**: Graceful handling of file read/write errors
- **API Errors**: Detailed error messages for API-related issues
- **Graceful Shutdown**: Handles Ctrl+C interruption cleanly

## Performance Considerations

- **API Rate Limits**: The script includes a 2-second delay between requests
- **File Size**: Large MP3 files may take longer to process
- **Concurrent Processing**: Currently processes files sequentially to respect API limits

## Troubleshooting

### Common Issues

1. **"No MP3 files found"**
   - Ensure you're running the script in the correct directory
   - Check that files have the `.mp3` extension (case-sensitive)

2. **API Key Errors**
   - Verify your OpenAI API key is valid and has Whisper API access
   - Check your OpenAI account has sufficient credits

3. **File Permission Errors**
   - Ensure the script has read access to MP3 files
   - Ensure the script has write access to create transcript files

4. **Large File Errors**
   - OpenAI Whisper API has a 25MB file size limit
   - Consider compressing or splitting larger files

## Dependencies

- **openai**: ^4.20.1 - Official OpenAI Node.js library

## License

MIT License - Feel free to modify and distribute as needed.

## Support

For issues related to:
- **OpenAI API**: Check the [OpenAI documentation](https://platform.openai.com/docs/api-reference/audio)
- **Script bugs**: Review the error messages and check file permissions
- **Hungarian language accuracy**: Consider adjusting the language parameter or using post-processing
