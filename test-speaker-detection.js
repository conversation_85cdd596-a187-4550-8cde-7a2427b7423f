#!/usr/bin/env node

/**
 * Test script to verify speaker detection logic
 */

const { detectSpeakerChanges, groupSegmentsBySpeaker, formatTranscription } = require('./transcribe-mp3.js');

// Mock transcription data to test speaker detection
const mockTranscriptionData = {
  text: "Hello, this is speaker one. Hi there, this is speaker two responding. Yes, I understand what you mean. That's a great point you made.",
  segments: [
    {
      start: 0.0,
      end: 2.5,
      text: "Hello, this is speaker one.",
      avg_logprob: -0.3,
      no_speech_prob: 0.1
    },
    {
      start: 3.0, // 0.5 second gap
      end: 6.2,
      text: "Hi there, this is speaker two responding.",
      avg_logprob: -0.7, // Different voice characteristics
      no_speech_prob: 0.15
    },
    {
      start: 6.5, // 0.3 second gap (same speaker continues)
      end: 9.1,
      text: "Yes, I understand what you mean.",
      avg_logprob: -0.65, // Similar to previous
      no_speech_prob: 0.12
    },
    {
      start: 10.5, // 1.4 second gap (speaker change)
      end: 13.2,
      text: "That's a great point you made.",
      avg_logprob: -0.35, // Back to first speaker characteristics
      no_speech_prob: 0.08
    }
  ]
};

function testSpeakerDetection() {
  console.log('🧪 Testing Speaker Detection Logic');
  console.log('='.repeat(50));
  
  console.log('\n📝 Mock transcription segments:');
  mockTranscriptionData.segments.forEach((segment, index) => {
    console.log(`${index + 1}. [${segment.start}s - ${segment.end}s] "${segment.text}"`);
    console.log(`   avg_logprob: ${segment.avg_logprob}, no_speech_prob: ${segment.no_speech_prob}`);
  });
  
  console.log('\n🔍 Testing speaker change detection...');
  const speakerChanges = detectSpeakerChanges(mockTranscriptionData.segments);
  console.log('Speaker assignments:', speakerChanges);
  
  console.log('\n👥 Testing segment grouping by speaker...');
  const groupedSegments = groupSegmentsBySpeaker(mockTranscriptionData.segments, speakerChanges);
  
  groupedSegments.forEach((group, index) => {
    console.log(`\nGroup ${index + 1} - Speaker ${group.speaker}:`);
    console.log(`  Time: ${group.startTime}s - ${group.endTime}s`);
    console.log(`  Text: "${group.text}"`);
    console.log(`  Segments: ${group.segments.length}`);
  });
  
  console.log('\n📄 Testing final SRT formatting...');
  const formattedOutput = formatTranscription(mockTranscriptionData);
  console.log('─'.repeat(30));
  console.log(formattedOutput);
  console.log('─'.repeat(30));
  
  console.log('\n✅ Speaker detection test completed!');
  console.log('\n💡 Expected behavior:');
  console.log('- Segment 1: Speaker 0 (first speaker)');
  console.log('- Segment 2: Speaker 1 (voice change detected)');
  console.log('- Segment 3: Speaker 1 (continues same speaker)');
  console.log('- Segment 4: Speaker 0 (back to first speaker)');
}

// Test with different configurations
function testWithDifferentConfigs() {
  console.log('\n🔧 Testing with different configurations...');
  
  const config = require('./config.js');
  
  // Test with simple detection
  console.log('\n1. Simple detection (silence gaps only):');
  const originalAdvanced = config.output.speakerDetection.useAdvancedDetection;
  config.output.speakerDetection.useAdvancedDetection = false;
  
  const simpleChanges = detectSpeakerChanges(mockTranscriptionData.segments);
  console.log('Simple speaker assignments:', simpleChanges);
  
  // Test with advanced detection
  console.log('\n2. Advanced detection (audio characteristics):');
  config.output.speakerDetection.useAdvancedDetection = true;
  
  const advancedChanges = detectSpeakerChanges(mockTranscriptionData.segments);
  console.log('Advanced speaker assignments:', advancedChanges);
  
  // Restore original setting
  config.output.speakerDetection.useAdvancedDetection = originalAdvanced;
}

if (require.main === module) {
  testSpeakerDetection();
  testWithDifferentConfigs();
}
