#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const OpenAI = require('openai');
const config = require('./config.js');

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

/**
 * Convert seconds to SRT timestamp format (HH:MM:SS,mmm)
 */
function secondsToSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Detect speaker changes based on audio characteristics and timing
 */
function detectSpeakerChanges(segments) {
  if (!segments || segments.length === 0) return [];

  const speakerChanges = [0]; // First segment is always Speaker 0
  let currentSpeaker = 0;

  // First pass: detect potential speaker changes
  const potentialChanges = [];

  for (let i = 1; i < segments.length; i++) {
    const currentSegment = segments[i];
    const prevSegment = segments[i - 1];

    // Calculate silence gap between segments
    const silenceGap = currentSegment.start - prevSegment.end;

    // Calculate segment characteristics for speaker detection
    const segmentDuration = currentSegment.end - currentSegment.start;
    const avgLogprob = currentSegment.avg_logprob || 0;
    const noSpeechProb = currentSegment.no_speech_prob || 0;

    // Speaker change detection logic
    let changeScore = 0;

    if (config.output.speakerDetection.useAdvancedDetection) {
      // Silence gap scoring
      if (silenceGap > config.output.speakerDetection.minSilenceGap) {
        changeScore += 2;
      }

      // Audio characteristics scoring
      const prevAvgLogprob = prevSegment.avg_logprob || 0;
      const logprobDiff = Math.abs(avgLogprob - prevAvgLogprob);

      if (logprobDiff > 0.3) {
        changeScore += 3; // Strong indicator
      } else if (logprobDiff > 0.15) {
        changeScore += 1; // Weak indicator
      }

      // Speech probability changes
      const prevNoSpeechProb = prevSegment.no_speech_prob || 0;
      const noSpeechProbDiff = Math.abs(noSpeechProb - prevNoSpeechProb);

      if (noSpeechProbDiff > 0.05) {
        changeScore += 1;
      }

      // Duration pattern changes (interruptions, responses)
      const prevDuration = prevSegment.end - prevSegment.start;
      if (prevDuration < 1.0 && segmentDuration > 2.0) {
        changeScore += 1; // Short question followed by long answer
      }

    } else {
      // Simple detection
      if (silenceGap > config.output.speakerDetection.minSilenceGap) {
        changeScore += 2;
      }

      const prevAvgLogprob = prevSegment.avg_logprob || 0;
      const logprobDiff = Math.abs(avgLogprob - prevAvgLogprob);

      if (logprobDiff > 0.25) {
        changeScore += 2;
      }
    }

    potentialChanges.push(changeScore);
  }

  // Second pass: apply speaker changes based on scores and context
  for (let i = 1; i < segments.length; i++) {
    const changeScore = potentialChanges[i - 1];

    // Require a minimum score to change speakers
    const threshold = config.output.speakerDetection.useAdvancedDetection ? 2 : 2;

    if (changeScore >= threshold) {
      // Look ahead to see if this makes sense in context
      let shouldChange = true;

      // Don't change if the next segment would immediately change back
      if (i < segments.length - 1) {
        const nextChangeScore = potentialChanges[i] || 0;
        if (nextChangeScore >= threshold) {
          // Two consecutive changes - might be noise, be more conservative
          shouldChange = changeScore >= threshold + 1;
        }
      }

      if (shouldChange) {
        currentSpeaker = currentSpeaker === 0 ? 1 : 0;
      }
    }

    speakerChanges.push(currentSpeaker);
  }

  return speakerChanges;
}

/**
 * Group consecutive segments by the same speaker
 */
function groupSegmentsBySpeaker(segments, speakerChanges) {
  if (!segments || segments.length === 0) return [];

  const groupedSegments = [];
  let currentGroup = {
    speaker: speakerChanges[0],
    segments: [segments[0]],
    startTime: segments[0].start,
    endTime: segments[0].end,
    text: segments[0].text.trim()
  };

  for (let i = 1; i < segments.length; i++) {
    const segment = segments[i];
    const speaker = speakerChanges[i];

    if (speaker === currentGroup.speaker) {
      // Same speaker, add to current group
      currentGroup.segments.push(segment);
      currentGroup.endTime = segment.end;
      currentGroup.text += ' ' + segment.text.trim();
    } else {
      // Speaker changed, finish current group and start new one
      groupedSegments.push(currentGroup);
      currentGroup = {
        speaker: speaker,
        segments: [segment],
        startTime: segment.start,
        endTime: segment.end,
        text: segment.text.trim()
      };
    }
  }

  // Add the last group
  groupedSegments.push(currentGroup);

  return groupedSegments;
}

/**
 * Format transcription data into SRT format with improved speaker labels
 */
function formatTranscription(transcriptionData) {
  if (!transcriptionData.segments || transcriptionData.segments.length === 0) {
    return transcriptionData.text || '';
  }

  // Detect speaker changes
  const speakerChanges = detectSpeakerChanges(transcriptionData.segments);

  // Group segments by speaker
  const groupedSegments = groupSegmentsBySpeaker(transcriptionData.segments, speakerChanges);

  let srtContent = '';

  groupedSegments.forEach((group) => {
    const startTime = secondsToSRTTime(group.startTime);
    const endTime = secondsToSRTTime(group.endTime);
    const speakerLabel = `[Speaker ${group.speaker}]`;
    const text = group.text.trim();

    if (text) {
      srtContent += `${startTime} --> ${endTime} ${speakerLabel}\n`;
      srtContent += `${text}\n\n`;
    }
  });

  return srtContent;
}

/**
 * Transcribe a single MP3 file using OpenAI API with model fallback
 */
async function transcribeFile(filePath, useModel = null) {
  const modelToUse = useModel || config.openai.model;

  try {
    console.log(`🎵 Processing: ${path.basename(filePath)}`);

    // Read the audio file
    const audioFile = await fs.readFile(filePath);

    // Create a File object for the API
    const file = new File([audioFile], path.basename(filePath), {
      type: 'audio/mpeg'
    });

    // Call OpenAI API
    console.log(`🔄 Sending to ${modelToUse} API...`);
    const transcription = await openai.audio.transcriptions.create({
      file: file,
      model: modelToUse,
      language: config.openai.language,
      response_format: config.openai.responseFormat,
      timestamp_granularities: ['segment']
    });

    console.log(`✅ Transcription completed for ${path.basename(filePath)} using ${modelToUse}`);
    return transcription;

  } catch (error) {
    console.error(`❌ Error transcribing ${path.basename(filePath)} with ${modelToUse}:`, error.message);

    // Handle specific API errors
    if (error.status === 429) {
      console.log(`⏳ Rate limit hit. Waiting ${config.processing.rateLimitRetryDelay / 1000} seconds before retrying...`);
      await new Promise(resolve => setTimeout(resolve, config.processing.rateLimitRetryDelay));
      return transcribeFile(filePath, modelToUse); // Retry with same model
    }

    // If the primary model fails and we haven't tried the fallback yet
    if (modelToUse === config.openai.model && config.openai.fallbackModel && modelToUse !== config.openai.fallbackModel) {
      console.log(`🔄 Trying fallback model: ${config.openai.fallbackModel}`);
      return transcribeFile(filePath, config.openai.fallbackModel);
    }

    throw error;
  }
}

/**
 * Save transcription to a text file
 */
async function saveTranscription(mp3FilePath, transcriptionData) {
  try {
    const baseName = path.basename(mp3FilePath, '.mp3');
    const outputPath = path.join(path.dirname(mp3FilePath), `${baseName}${config.output.transcriptExtension}`);
    
    const formattedContent = formatTranscription(transcriptionData);
    
    await fs.writeFile(outputPath, formattedContent, 'utf8');
    console.log(`💾 Saved transcript: ${path.basename(outputPath)}`);
    
    return outputPath;
  } catch (error) {
    console.error(`❌ Error saving transcript for ${path.basename(mp3FilePath)}:`, error.message);
    throw error;
  }
}

/**
 * Find all MP3 files in the current directory
 */
async function findMP3Files(directory = '.') {
  try {
    const files = await fs.readdir(directory);
    const mp3Files = files
      .filter(file => path.extname(file).toLowerCase() === '.mp3')
      .map(file => path.join(directory, file));
    
    return mp3Files;
  } catch (error) {
    console.error('❌ Error reading directory:', error.message);
    throw error;
  }
}

/**
 * Main function to process all MP3 files
 */
async function main() {
  try {
    console.log('🚀 Starting MP3 transcription process...');
    console.log(`📁 Working directory: ${process.cwd()}`);
    
    // Find all MP3 files
    const mp3Files = await findMP3Files();
    
    if (mp3Files.length === 0) {
      console.log('📭 No MP3 files found in the current directory.');
      return;
    }
    
    console.log(`📋 Found ${mp3Files.length} MP3 files to process:`);
    mp3Files.forEach((file, index) => {
      console.log(`   ${index + 1}. ${path.basename(file)}`);
    });
    console.log('');

    let successCount = 0;
    let errorCount = 0;

    // Process each MP3 file
    for (let i = 0; i < mp3Files.length; i++) {
      const mp3File = mp3Files[i];
      console.log(`\n📊 Progress: ${i + 1}/${mp3Files.length}`);
      console.log('─'.repeat(50));
      
      try {
        // Check if transcript already exists
        const baseName = path.basename(mp3File, '.mp3');
        const transcriptPath = path.join(path.dirname(mp3File), `${baseName}${config.output.transcriptExtension}`);

        if (config.processing.skipExisting) {
          try {
            await fs.access(transcriptPath);
            console.log(`⏭️  Transcript already exists for ${path.basename(mp3File)}, skipping...`);
            continue;
          } catch {
            // File doesn't exist, proceed with transcription
          }
        }

        // Transcribe the file
        const transcription = await transcribeFile(mp3File);
        
        // Save the transcript
        await saveTranscription(mp3File, transcription);
        
        successCount++;
        
        // Add a small delay between requests to be respectful to the API
        if (i < mp3Files.length - 1) {
          console.log(`⏳ Waiting ${config.processing.delayBetweenRequests / 1000} seconds before next file...`);
          await new Promise(resolve => setTimeout(resolve, config.processing.delayBetweenRequests));
        }
        
      } catch (error) {
        console.error(`❌ Failed to process ${path.basename(mp3File)}:`, error.message);
        errorCount++;
      }
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📈 TRANSCRIPTION SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Successfully processed: ${successCount} files`);
    console.log(`❌ Failed to process: ${errorCount} files`);
    console.log(`📁 Total files found: ${mp3Files.length}`);
    
    if (successCount > 0) {
      console.log('\n🎉 Transcription process completed successfully!');
      console.log('📝 Check the generated .txt files for your transcripts.');
    }

  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Process interrupted by user. Exiting gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️  Process terminated. Exiting gracefully...');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  transcribeFile,
  saveTranscription,
  findMP3Files,
  formatTranscription,
  secondsToSRTTime,
  detectSpeakerChanges,
  groupSegmentsBySpeaker
};
