#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const OpenAI = require('openai');
const config = require('./config.js');

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

/**
 * Convert seconds to SRT timestamp format (HH:MM:SS,mmm)
 */
function secondsToSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Detect speaker changes based on Hungarian conversation patterns and audio characteristics
 */
function detectSpeakerChanges(segments) {
  if (!segments || segments.length === 0) return [];

  const speakerChanges = [0]; // First segment is always Speaker 0
  let currentSpeaker = 0;

  // Hungarian conversation patterns for speaker detection
  const hungarianPatterns = {
    questions: /\b(mi|ki|hol|mikor|miért|hogyan|milyen|mit|kinek|hova|merre|mennyi|melyik)\b|\?/i,
    shortResponses: /\b(igen|nem|ja|jó|oké|persze|természetesen|nyilván|hát|nos|szóval)\b/i,
    interjections: /\b(mama|papa|hé|na|jaj|ó|ah|eh|hmm|igen\?|nem\?|ja\?)\b/i,
    laughter: /\[nevet\]|\[nevetés\]|\[kacag\]/i
  };

  // First pass: detect potential speaker changes
  const potentialChanges = [];

  for (let i = 1; i < segments.length; i++) {
    const currentSegment = segments[i];
    const prevSegment = segments[i - 1];

    // Calculate silence gap between segments
    const silenceGap = currentSegment.start - prevSegment.end;

    // Calculate segment characteristics for speaker detection
    const segmentDuration = currentSegment.end - currentSegment.start;
    const avgLogprob = currentSegment.avg_logprob || 0;
    const noSpeechProb = currentSegment.no_speech_prob || 0;
    const text = currentSegment.text.toLowerCase().trim();
    const prevText = prevSegment.text.toLowerCase().trim();

    // Speaker change detection logic
    let changeScore = 0;

    // Hungarian conversation pattern analysis
    const prevDuration = prevSegment.end - prevSegment.start;

    // Pattern 1: Short questions or interjections
    if (segmentDuration < 3.0) {
      if (hungarianPatterns.questions.test(text)) {
        changeScore += 3; // Questions are strong indicators
      }
      if (hungarianPatterns.interjections.test(text)) {
        changeScore += 3; // Interjections like "Mama!"
      }
      if (hungarianPatterns.shortResponses.test(text)) {
        changeScore += 2; // Short responses
      }
      if (hungarianPatterns.laughter.test(text)) {
        changeScore += 3; // Laughter annotations
      }
    }

    // Pattern 2: Return to main speaker after short segment
    if (prevDuration < 3.0 && segmentDuration > 4.0) {
      changeScore += 2; // Likely return to main speaker
    }

    // Pattern 3: Significant silence gaps
    if (silenceGap > 0.5) {
      changeScore += 1;
    }
    if (silenceGap > 1.0) {
      changeScore += 2;
    }

    if (config.output.speakerDetection.useAdvancedDetection) {
      // Audio characteristics scoring
      const prevAvgLogprob = prevSegment.avg_logprob || 0;
      const logprobDiff = Math.abs(avgLogprob - prevAvgLogprob);

      if (logprobDiff > 0.3) {
        changeScore += 2; // Strong indicator
      } else if (logprobDiff > 0.1) {
        changeScore += 1; // Weak indicator
      }

      // Speech probability changes
      const prevNoSpeechProb = prevSegment.no_speech_prob || 0;
      const noSpeechProbDiff = Math.abs(noSpeechProb - prevNoSpeechProb);

      if (noSpeechProbDiff > 0.05) {
        changeScore += 1;
      }

      // Context analysis: Look for conversation flow patterns
      if (i > 1) {
        const prevPrevSegment = segments[i - 2];
        const prevPrevDuration = prevPrevSegment.end - prevPrevSegment.start;

        // Pattern: Long -> Short -> Long (typical interview pattern)
        if (prevPrevDuration > 4.0 && prevDuration < 3.0 && segmentDuration > 4.0) {
          changeScore += 1.5;
        }
      }
    }

    potentialChanges.push(changeScore);
  }

  // Second pass: apply speaker changes based on scores and context
  // Interview pattern: Speaker 0 = main speaker (interviewee), Speaker 1 = interviewer
  for (let i = 1; i < segments.length; i++) {
    const changeScore = potentialChanges[i - 1];
    const currentSegment = segments[i];
    const segmentDuration = currentSegment.end - currentSegment.start;
    const text = currentSegment.text.toLowerCase().trim();

    // Dynamic threshold based on segment characteristics
    let threshold = 1.5;

    // Identify interviewer patterns (short questions, interjections)
    const isLikelyInterviewer = (
      segmentDuration < 3.0 && (
        /\b(mama|papa|igen\?|nem\?|ja\?|mi\?|ki\?|hol\?|mikor\?|miért\?|hogyan\?|milyen\?)\b/i.test(text) ||
        /\[nevet\]/i.test(text) ||
        text.includes('?') ||
        /\b(igen|nem|ja|jó|oké|persze)\b/i.test(text) && segmentDuration < 2.0
      )
    );

    // Lower threshold for obvious interviewer interjections
    if (isLikelyInterviewer) {
      threshold = 1.0;
      // Force speaker change to interviewer (Speaker 1) for short interjections
      if (currentSpeaker === 0 && changeScore >= threshold) {
        currentSpeaker = 1;
      }
    } else if (changeScore >= threshold) {
      // For longer segments, alternate normally
      currentSpeaker = currentSpeaker === 0 ? 1 : 0;
    }

    // Special case: after interviewer interjection, return to main speaker
    if (i > 1) {
      const prevSegment = segments[i - 1];
      const prevDuration = prevSegment.end - prevSegment.start;
      const prevText = prevSegment.text.toLowerCase().trim();

      if (prevDuration < 3.0 && segmentDuration > 4.0 &&
          (/\?/.test(prevText) || /\b(igen|nem|ja)\b/i.test(prevText))) {
        currentSpeaker = 0; // Return to main speaker (interviewee)
      }
    }

    speakerChanges.push(currentSpeaker);
  }

  return speakerChanges;
}

/**
 * Group consecutive segments by the same speaker
 */
function groupSegmentsBySpeaker(segments, speakerChanges) {
  if (!segments || segments.length === 0) return [];

  const groupedSegments = [];
  let currentGroup = {
    speaker: speakerChanges[0],
    segments: [segments[0]],
    startTime: segments[0].start,
    endTime: segments[0].end,
    text: segments[0].text.trim()
  };

  for (let i = 1; i < segments.length; i++) {
    const segment = segments[i];
    const speaker = speakerChanges[i];

    if (speaker === currentGroup.speaker) {
      // Same speaker, add to current group
      currentGroup.segments.push(segment);
      currentGroup.endTime = segment.end;
      currentGroup.text += ' ' + segment.text.trim();
    } else {
      // Speaker changed, finish current group and start new one
      groupedSegments.push(currentGroup);
      currentGroup = {
        speaker: speaker,
        segments: [segment],
        startTime: segment.start,
        endTime: segment.end,
        text: segment.text.trim()
      };
    }
  }

  // Add the last group
  groupedSegments.push(currentGroup);

  return groupedSegments;
}

/**
 * Format transcription data into SRT format with improved speaker labels
 */
function formatTranscription(transcriptionData) {
  // Handle different response formats
  if (transcriptionData.segments && transcriptionData.segments.length > 0) {
    // verbose_json format with segments (whisper-1)
    return formatWithSegments(transcriptionData);
  } else if (transcriptionData.text) {
    // json or text format without segments (gpt-4o-transcribe)
    return formatWithoutSegments(transcriptionData);
  } else {
    return transcriptionData.text || '';
  }
}

/**
 * Format transcription with detailed segments (verbose_json format)
 */
function formatWithSegments(transcriptionData) {
  // Detect speaker changes
  const speakerChanges = detectSpeakerChanges(transcriptionData.segments);

  // Group segments by speaker
  const groupedSegments = groupSegmentsBySpeaker(transcriptionData.segments, speakerChanges);

  let srtContent = '';

  groupedSegments.forEach((group) => {
    const startTime = secondsToSRTTime(group.startTime);
    const endTime = secondsToSRTTime(group.endTime);
    const speakerLabel = `[Speaker ${group.speaker}]`;
    const text = group.text.trim();

    if (text) {
      srtContent += `${startTime} --> ${endTime} ${speakerLabel}\n`;
      srtContent += `${text}\n\n`;
    }
  });

  return srtContent;
}

/**
 * Format transcription without segments (json/text format)
 * Uses basic heuristics to detect speaker changes
 */
function formatWithoutSegments(transcriptionData) {
  const text = transcriptionData.text || '';

  if (!text.trim()) {
    return '';
  }

  // For now, create a simple format with the entire text as Speaker 0
  // In a real implementation, you might want to use additional AI processing
  // to detect speaker changes in the text itself

  const startTime = secondsToSRTTime(0);
  const endTime = secondsToSRTTime(60); // Placeholder - we don't have timing info

  let srtContent = '';
  srtContent += `${startTime} --> ${endTime} [Speaker 0]\n`;
  srtContent += `${text.trim()}\n\n`;

  console.log('⚠️  Note: Using simplified format due to limited timing information from gpt-4o-transcribe');
  console.log('💡 For detailed speaker detection and timing, consider using whisper-1 model');

  return srtContent;
}

/**
 * Transcribe a single MP3 file using OpenAI API with model fallback
 */
async function transcribeFile(filePath, useModel = null) {
  const modelToUse = useModel || config.openai.model;

  try {
    console.log(`🎵 Processing: ${path.basename(filePath)}`);

    // Read the audio file
    const audioFile = await fs.readFile(filePath);

    // Create a File object for the API
    const file = new File([audioFile], path.basename(filePath), {
      type: 'audio/mpeg'
    });

    // Determine response format based on model
    let responseFormat, timestampGranularities;

    if (modelToUse === 'whisper-1') {
      responseFormat = config.openai.responseFormat; // 'verbose_json'
      timestampGranularities = ['segment']; // whisper-1 supports detailed segments
    } else if (modelToUse === 'gpt-4o-transcribe') {
      responseFormat = config.openai.fallbackResponseFormat; // 'json'
      timestampGranularities = undefined; // gpt-4o-transcribe doesn't support this parameter
    } else {
      // Default fallback
      responseFormat = 'verbose_json';
      timestampGranularities = ['segment'];
    }

    // Call OpenAI API
    console.log(`🔄 Sending to ${modelToUse} API with format: ${responseFormat}...`);

    const apiParams = {
      file: file,
      model: modelToUse,
      language: config.openai.language,
      response_format: responseFormat
    };

    // Only add timestamp_granularities for models that support it
    if (timestampGranularities) {
      apiParams.timestamp_granularities = timestampGranularities;
    }

    const transcription = await openai.audio.transcriptions.create(apiParams);

    console.log(`✅ Transcription completed for ${path.basename(filePath)} using ${modelToUse}`);
    return transcription;

  } catch (error) {
    console.error(`❌ Error transcribing ${path.basename(filePath)} with ${modelToUse}:`, error.message);

    // Handle specific API errors
    if (error.status === 429) {
      console.log(`⏳ Rate limit hit. Waiting ${config.processing.rateLimitRetryDelay / 1000} seconds before retrying...`);
      await new Promise(resolve => setTimeout(resolve, config.processing.rateLimitRetryDelay));
      return transcribeFile(filePath, modelToUse); // Retry with same model
    }

    // If the primary model fails and we haven't tried the fallback yet
    if (modelToUse === config.openai.model && config.openai.fallbackModel && modelToUse !== config.openai.fallbackModel) {
      console.log(`🔄 Trying fallback model: ${config.openai.fallbackModel}`);
      return transcribeFile(filePath, config.openai.fallbackModel);
    }

    throw error;
  }
}

/**
 * Save transcription to a text file
 */
async function saveTranscription(mp3FilePath, transcriptionData) {
  try {
    const baseName = path.basename(mp3FilePath, '.mp3');
    const outputPath = path.join(path.dirname(mp3FilePath), `${baseName}${config.output.transcriptExtension}`);
    
    const formattedContent = formatTranscription(transcriptionData);
    
    await fs.writeFile(outputPath, formattedContent, 'utf8');
    console.log(`💾 Saved transcript: ${path.basename(outputPath)}`);
    
    return outputPath;
  } catch (error) {
    console.error(`❌ Error saving transcript for ${path.basename(mp3FilePath)}:`, error.message);
    throw error;
  }
}

/**
 * Find all MP3 files in the current directory
 */
async function findMP3Files(directory = '.') {
  try {
    const files = await fs.readdir(directory);
    const mp3Files = files
      .filter(file => path.extname(file).toLowerCase() === '.mp3')
      .map(file => path.join(directory, file));
    
    return mp3Files;
  } catch (error) {
    console.error('❌ Error reading directory:', error.message);
    throw error;
  }
}

/**
 * Main function to process all MP3 files
 */
async function main() {
  try {
    console.log('🚀 Starting MP3 transcription process...');
    console.log(`📁 Working directory: ${process.cwd()}`);
    
    // Find all MP3 files
    const mp3Files = await findMP3Files();
    
    if (mp3Files.length === 0) {
      console.log('📭 No MP3 files found in the current directory.');
      return;
    }
    
    console.log(`📋 Found ${mp3Files.length} MP3 files to process:`);
    mp3Files.forEach((file, index) => {
      console.log(`   ${index + 1}. ${path.basename(file)}`);
    });
    console.log('');

    let successCount = 0;
    let errorCount = 0;

    // Process each MP3 file
    for (let i = 0; i < mp3Files.length; i++) {
      const mp3File = mp3Files[i];
      console.log(`\n📊 Progress: ${i + 1}/${mp3Files.length}`);
      console.log('─'.repeat(50));
      
      try {
        // Check if transcript already exists
        const baseName = path.basename(mp3File, '.mp3');
        const transcriptPath = path.join(path.dirname(mp3File), `${baseName}${config.output.transcriptExtension}`);

        if (config.processing.skipExisting) {
          try {
            await fs.access(transcriptPath);
            console.log(`⏭️  Transcript already exists for ${path.basename(mp3File)}, skipping...`);
            continue;
          } catch {
            // File doesn't exist, proceed with transcription
          }
        }

        // Transcribe the file
        const transcription = await transcribeFile(mp3File);
        
        // Save the transcript
        await saveTranscription(mp3File, transcription);
        
        successCount++;
        
        // Add a small delay between requests to be respectful to the API
        if (i < mp3Files.length - 1) {
          console.log(`⏳ Waiting ${config.processing.delayBetweenRequests / 1000} seconds before next file...`);
          await new Promise(resolve => setTimeout(resolve, config.processing.delayBetweenRequests));
        }
        
      } catch (error) {
        console.error(`❌ Failed to process ${path.basename(mp3File)}:`, error.message);
        errorCount++;
      }
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📈 TRANSCRIPTION SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Successfully processed: ${successCount} files`);
    console.log(`❌ Failed to process: ${errorCount} files`);
    console.log(`📁 Total files found: ${mp3Files.length}`);
    
    if (successCount > 0) {
      console.log('\n🎉 Transcription process completed successfully!');
      console.log('📝 Check the generated .txt files for your transcripts.');
    }

  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Process interrupted by user. Exiting gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️  Process terminated. Exiting gracefully...');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  transcribeFile,
  saveTranscription,
  findMP3Files,
  formatTranscription,
  secondsToSRTTime,
  detectSpeakerChanges,
  groupSegmentsBySpeaker
};
