#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const OpenAI = require('openai');
const config = require('./config.js');

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

/**
 * Convert seconds to SRT timestamp format (HH:MM:SS,mmm)
 */
function secondsToSRTTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

/**
 * Format transcription data into SRT format with speaker labels
 */
function formatTranscription(transcriptionData) {
  if (!transcriptionData.segments || transcriptionData.segments.length === 0) {
    return transcriptionData.text || '';
  }

  let srtContent = '';
  let speakerCounter = 0;
  const speakerMap = new Map();

  transcriptionData.segments.forEach((segment, index) => {
    const startTime = secondsToSRTTime(segment.start);
    const endTime = secondsToSRTTime(segment.end);
    
    // Simple speaker detection based on silence gaps (basic heuristic)
    // In a real implementation, you might want to use more sophisticated speaker diarization
    let speakerId = 0;
    if (index > 0) {
      const prevSegment = transcriptionData.segments[index - 1];
      const silenceGap = segment.start - prevSegment.end;
      
      // If there's a significant gap, assume speaker change
      if (silenceGap > config.processing.speakerChangeThreshold) {
        speakerId = (speakerId + 1) % config.output.maxSpeakers; // Alternate between speakers
      }
    }
    
    const speakerLabel = `[Speaker ${speakerId}]`;
    const text = segment.text.trim();
    
    if (text) {
      srtContent += `${startTime} --> ${endTime} ${speakerLabel}\n`;
      srtContent += `${text}\n\n`;
    }
  });

  return srtContent;
}

/**
 * Transcribe a single MP3 file using OpenAI Whisper API
 */
async function transcribeFile(filePath) {
  try {
    console.log(`🎵 Processing: ${path.basename(filePath)}`);
    
    // Read the audio file
    const audioFile = await fs.readFile(filePath);
    
    // Create a File object for the API
    const file = new File([audioFile], path.basename(filePath), {
      type: 'audio/mpeg'
    });

    // Call OpenAI Whisper API
    console.log(`🔄 Sending to Whisper API...`);
    const transcription = await openai.audio.transcriptions.create({
      file: file,
      model: config.openai.model,
      language: config.openai.language,
      response_format: config.openai.responseFormat,
      timestamp_granularities: ['segment']
    });

    console.log(`✅ Transcription completed for ${path.basename(filePath)}`);
    return transcription;

  } catch (error) {
    console.error(`❌ Error transcribing ${path.basename(filePath)}:`, error.message);
    
    // Handle specific API errors
    if (error.status === 429) {
      console.log(`⏳ Rate limit hit. Waiting ${config.processing.rateLimitRetryDelay / 1000} seconds before retrying...`);
      await new Promise(resolve => setTimeout(resolve, config.processing.rateLimitRetryDelay));
      return transcribeFile(filePath); // Retry
    }
    
    throw error;
  }
}

/**
 * Save transcription to a text file
 */
async function saveTranscription(mp3FilePath, transcriptionData) {
  try {
    const baseName = path.basename(mp3FilePath, '.mp3');
    const outputPath = path.join(path.dirname(mp3FilePath), `${baseName}${config.output.transcriptExtension}`);
    
    const formattedContent = formatTranscription(transcriptionData);
    
    await fs.writeFile(outputPath, formattedContent, 'utf8');
    console.log(`💾 Saved transcript: ${path.basename(outputPath)}`);
    
    return outputPath;
  } catch (error) {
    console.error(`❌ Error saving transcript for ${path.basename(mp3FilePath)}:`, error.message);
    throw error;
  }
}

/**
 * Find all MP3 files in the current directory
 */
async function findMP3Files(directory = '.') {
  try {
    const files = await fs.readdir(directory);
    const mp3Files = files
      .filter(file => path.extname(file).toLowerCase() === '.mp3')
      .map(file => path.join(directory, file));
    
    return mp3Files;
  } catch (error) {
    console.error('❌ Error reading directory:', error.message);
    throw error;
  }
}

/**
 * Main function to process all MP3 files
 */
async function main() {
  try {
    console.log('🚀 Starting MP3 transcription process...');
    console.log(`📁 Working directory: ${process.cwd()}`);
    
    // Find all MP3 files
    const mp3Files = await findMP3Files();
    
    if (mp3Files.length === 0) {
      console.log('📭 No MP3 files found in the current directory.');
      return;
    }
    
    console.log(`📋 Found ${mp3Files.length} MP3 files to process:`);
    mp3Files.forEach((file, index) => {
      console.log(`   ${index + 1}. ${path.basename(file)}`);
    });
    console.log('');

    let successCount = 0;
    let errorCount = 0;

    // Process each MP3 file
    for (let i = 0; i < mp3Files.length; i++) {
      const mp3File = mp3Files[i];
      console.log(`\n📊 Progress: ${i + 1}/${mp3Files.length}`);
      console.log('─'.repeat(50));
      
      try {
        // Check if transcript already exists
        const baseName = path.basename(mp3File, '.mp3');
        const transcriptPath = path.join(path.dirname(mp3File), `${baseName}${config.output.transcriptExtension}`);

        if (config.processing.skipExisting) {
          try {
            await fs.access(transcriptPath);
            console.log(`⏭️  Transcript already exists for ${path.basename(mp3File)}, skipping...`);
            continue;
          } catch {
            // File doesn't exist, proceed with transcription
          }
        }

        // Transcribe the file
        const transcription = await transcribeFile(mp3File);
        
        // Save the transcript
        await saveTranscription(mp3File, transcription);
        
        successCount++;
        
        // Add a small delay between requests to be respectful to the API
        if (i < mp3Files.length - 1) {
          console.log(`⏳ Waiting ${config.processing.delayBetweenRequests / 1000} seconds before next file...`);
          await new Promise(resolve => setTimeout(resolve, config.processing.delayBetweenRequests));
        }
        
      } catch (error) {
        console.error(`❌ Failed to process ${path.basename(mp3File)}:`, error.message);
        errorCount++;
      }
    }

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📈 TRANSCRIPTION SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Successfully processed: ${successCount} files`);
    console.log(`❌ Failed to process: ${errorCount} files`);
    console.log(`📁 Total files found: ${mp3Files.length}`);
    
    if (successCount > 0) {
      console.log('\n🎉 Transcription process completed successfully!');
      console.log('📝 Check the generated .txt files for your transcripts.');
    }

  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Process interrupted by user. Exiting gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️  Process terminated. Exiting gracefully...');
  process.exit(0);
});

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  transcribeFile,
  saveTranscription,
  findMP3Files,
  formatTranscription,
  secondsToSRTTime
};
